from flask import Flask, request, send_file, render_template, send_from_directory
import os
from ocr_pdf_img import ocr_pdf_img
import uuid

app = Flask(__name__)

# 确保pdfs和output目录存在
os.makedirs("pdfs", exist_ok=True)
os.makedirs("output", exist_ok=True)

@app.route('/mineru')
def index():
    return render_template('index.html')
@app.route('/mineru/js/<path:filename>')
def serve_js(filename):
    return send_from_directory('templates', filename)

@app.route('/mineru/download-sample')
def download_sample():
    """下载示例PDF文件"""
    try:
        # 检查simple.pdf文件是否存在
        sample_file_path = 'simple.pdf'
        if os.path.exists(sample_file_path):
            return send_file(sample_file_path, as_attachment=True, download_name='simple.pdf')
        else:
            return {"error": "示例文件不存在"}, 404
    except Exception as e:
        return {"error": f"下载失败: {str(e)}"}, 500

@app.route('/mineru/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return {"error": "No file part"}, 400

    # 获取vl_type参数，默认为'vl'
    vl_type = request.form.get('vl_type', 'vl')
    if vl_type not in ['vl', 'ocr']:
        return {"error": "Invalid vl_type, must be 'vl' or 'ocr'"}, 400

    # 获取formula_enable参数，默认为False
    formula_enable = request.form.get('formula_enable', 'false').lower() == 'true'
    if not isinstance(formula_enable, bool):
        return {"error": "Invalid formula_enable, must be 'true' or 'false'"}, 400

    file = request.files['file']
    if file.filename == '':
        return {"error": "No selected file"}, 400

    allowed_extensions = {'.pdf', '.docx', '.doc', '.pptx', 'ppt', '.jpeg', '.png', '.jpg'}
    file_ext = os.path.splitext(file.filename.lower())[1]
    if file_ext not in allowed_extensions:
        return {"error": f"Only {', '.join(allowed_extensions)} files are allowed"}, 400

    # 生成唯一文件名
    unique_id = str(uuid.uuid4())
    file_path = os.path.join("pdfs", f"{unique_id}{file_ext}")
    file.save(file_path)

    try:
        # 处理PDF文件
        output_dir = os.path.join("output", unique_id)
        os.makedirs(output_dir, exist_ok=True)

        md_file = ocr_pdf_img(file_path, output_dir, vl_type=vl_type, formula_enable=formula_enable)

        return {
            "status": "success",
            "markdown": md_file,
        }
    except Exception as e:
        return {"error": "处理失败！ "+str(e)}, 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5004)
